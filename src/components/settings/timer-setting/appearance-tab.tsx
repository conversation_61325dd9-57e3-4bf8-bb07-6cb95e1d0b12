'use client';

import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Timer, Check, Eye, BarChart3, Clock } from 'lucide-react';
import { Slider } from '@/components/ui/slider';
import { Input } from '@/components/ui/input';
import { Badge } from "@/components/ui/badge";
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { AppearanceTabProps } from './types';
import { TimerColorPreset, TimerUIStyle } from '@/lib/pomodoro-store';
import { cn } from '@/lib/utils';
import { motion, AnimatePresence } from 'framer-motion';
import { useState, useEffect } from 'react';

// Define timer color options
const colorOptions: {value: TimerColorPreset; label: string}[] = [
  { value: 'default', label: 'Default' },
  { value: 'white', label: 'White' },
  { value: 'blue', label: 'Blue' },
  { value: 'green', label: 'Green' },
  { value: 'yellow', label: 'Yellow' },
  { value: 'red', label: 'Red' },
  { value: 'purple', label: 'Purple' },
  // { value: 'indigo', label: 'Indigo' },
  { value: 'pink', label: 'Pink' },
  { value: 'orange', label: 'Orange' },
];

// Define UI style options
const uiStyleOptions: Array<{ value: TimerUIStyle; label: string; description: string }> = [
  {
    value: 'default',
    label: 'Minimal',
    description: 'Simple clean design with progress bar'
  },
  {
    value: 'circular',
    label: 'Circular',
    description: 'Circular timer with progress ring'
  },
];

// Color mapping for preview
const colorMapping = {
  default: 'white', // Default uses white color for preview
  white: 'white',
  blue: '#3b82f6',
  green: '#10b981',
  yellow: '#f59e0b',
  red: '#ef4444',
  purple: '#a855f7',
  indigo: '#6366f1',
  pink: '#ec4899',
  orange: '#f97316'
};

export function AppearanceTab({
  timerColor,
  setTimerColor,
  timerOpacity,
  handleOpacityChange,
  timerUIStyle,
  setTimerUIStyle,
  showProgressBar,
  setShowProgressBar,
  showCurrentTime,
  setShowCurrentTime
}: AppearanceTabProps) {
  // Animation state for timer preview
  const [isHovered, setIsHovered] = useState(false);
  const [animatedProgress, setAnimatedProgress] = useState(25);
  const [animatedTime, setAnimatedTime] = useState("25:00");

  // Enhanced styling classes for better visual hierarchy
  const sectionClass = "space-y-3";
  const sectionHeaderClass = "flex items-center gap-2.5 mb-3";
  const sectionIconClass = "flex items-center justify-center w-7 h-7 rounded-full bg-primary/10 text-primary";
  const sectionTitleClass = "text-sm font-semibold text-foreground";
  const settingItemClass = "space-y-2 pl-2 border-l-2 border-border/30";
  const settingControlClass = "flex items-center justify-between";
  const settingLabelClass = "flex items-center gap-2";
  const settingIconClass = "h-3.5 w-3.5 text-muted-foreground/70";
  const settingTextClass = "text-xs font-medium text-foreground/90";
  const descriptionClass = "text-xs text-muted-foreground leading-relaxed pl-5";

  // Background pattern for preview
  const gridPatternClass = cn(
    "bg-[length:20px_20px]",
    "bg-[radial-gradient(circle,rgba(0,0,0,0.03)_1px,transparent_1px)]",
    "dark:bg-[radial-gradient(circle,rgba(255,255,255,0.03)_1px,transparent_1px)]"
  );

  // Animate timer preview on hover
  useEffect(() => {
    if (isHovered) {
      const interval = setInterval(() => {
        setAnimatedProgress(prev => {
          const newProgress = prev >= 100 ? 0 : prev + 2;
          // Update time based on progress (simulate countdown)
          const totalSeconds = Math.floor((100 - newProgress) * 15); // 25 minutes = 1500 seconds
          const minutes = Math.floor(totalSeconds / 60);
          const seconds = totalSeconds % 60;
          setAnimatedTime(`${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`);
          return newProgress;
        });
      }, 50);
      return () => clearInterval(interval);
    } else {
      setAnimatedProgress(25);
      setAnimatedTime("25:00");
    }
  }, [isHovered]);

  return (
    <div className="space-y-4">
      {/* Timer UI Style Section */}
      <div className={sectionClass}>
        <div className={sectionHeaderClass}>
          <div className={sectionIconClass}>
            <Timer className="h-4 w-4" />
          </div>
          <h3 className={sectionTitleClass}>Timer Style</h3>
        </div>

        <div className="flex gap-1.5">
          {uiStyleOptions.map((option) => (
            <div
              key={option.value}
              className={`flex-1 flex flex-col p-2 border rounded-sm cursor-pointer transition-all hover:shadow-sm ${
                timerUIStyle === option.value
                  ? 'bg-primary/5 border-primary/30 ring-1 ring-primary/20'
                  : 'bg-background/50 border-border/50 hover:bg-muted/30'
              }`}
              onClick={() => setTimerUIStyle(option.value)}
            >
              <div className="flex items-center justify-between w-full">
                <span className="font-medium text-xs">{option.label}</span>
                {timerUIStyle === option.value && (
                  <Check className="h-3 w-3 text-primary/70" />
                )}
              </div>
              <span className="text-[10px] text-muted-foreground text-left leading-tight mt-0.5">
                {option.description}
              </span>
            </div>
          ))}
        </div>
      </div>

      {/* Display Options Section */}
      <div className={sectionClass}>
        <div className={sectionHeaderClass}>
          <div className={sectionIconClass}>
            <Eye className="h-4 w-4" />
          </div>
          <h3 className={sectionTitleClass}>Display Options</h3>
        </div>

        <div className="space-y-4">
          {/* Show Progress Bar Toggle */}
          <div className={settingItemClass}>
            <div className={settingControlClass}>
              <div className={settingLabelClass}>
                <BarChart3 className={settingIconClass} />
                <span className={settingTextClass}>Show progress bar</span>
              </div>
              <Switch
                checked={showProgressBar}
                onCheckedChange={setShowProgressBar}
                className="data-[state=checked]:bg-primary"
                aria-label="Toggle progress bar visibility"
              />
            </div>
            <p className={descriptionClass}>
              Display a visual progress indicator showing timer completion
            </p>
          </div>

          {/* Show Current Time Toggle */}
          <div className={settingItemClass}>
            <div className={settingControlClass}>
              <div className={settingLabelClass}>
                <Clock className={settingIconClass} />
                <span className={settingTextClass}>Show current time</span>
              </div>
              <Switch
                checked={showCurrentTime}
                onCheckedChange={setShowCurrentTime}
                className="data-[state=checked]:bg-primary"
                aria-label="Toggle current time display"
              />
            </div>
            <p className={descriptionClass}>
              Display the current system time alongside the timer
            </p>
          </div>
        </div>
      </div>

      <Separator className="my-6" />

      {/* Timer Color */}
      <div className={sectionClass}>
        <div className={sectionHeaderClass}>
          <div className={sectionIconClass}>
            <Paintbrush className="h-4 w-4" />
          </div>
          <h3 className={sectionTitleClass}>Timer Color</h3>
          <Badge variant="outline" className="h-5 px-2 text-xs font-normal capitalize bg-muted/30 border-border/50 ml-auto">
            {timerColor}
          </Badge>
        </div>

        <div className="grid grid-cols-3 gap-1.5">
          {colorOptions.map((color) => (
            <div
              key={color.value}
              className={`flex items-center justify-center p-2 border rounded-md cursor-pointer transition-all hover:shadow-sm ${
                timerColor === color.value
                  ? 'bg-primary/5 border-primary/30 ring-1 ring-primary/20'
                  : 'bg-background/50 border-border/50 hover:bg-muted/30'
              }`}
              onClick={() => setTimerColor(color.value)}
            >
              <div className="flex items-center gap-1.5">
                <div className={`h-3 w-3 rounded-full ${
                  color.value === 'default' ? 'bg-gradient-to-r from-blue-500 via-green-500 to-purple-500 border border-gray-300' :
                  color.value === 'white' ? 'bg-white border border-gray-300' :
                  color.value === 'blue' ? 'bg-blue-500' :
                  color.value === 'green' ? 'bg-green-500' :
                  color.value === 'yellow' ? 'bg-yellow-400' :
                  color.value === 'red' ? 'bg-red-500' :
                  color.value === 'purple' ? 'bg-purple-500' :
                  color.value === 'indigo' ? 'bg-indigo-500' :
                  color.value === 'pink' ? 'bg-pink-500' :
                  'bg-orange-500'
                }`}></div>
                <span className="text-xs">{color.label}</span>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Timer Opacity */}
      <div className={sectionClass}>
        <div className={sectionHeaderClass}>
          <div className={sectionIconClass}>
            <Layers className="h-4 w-4" />
          </div>
          <h3 className={sectionTitleClass}>Background Opacity</h3>
          <Badge variant="outline" className="h-5 px-2 text-xs font-normal bg-muted/30 border-border/50 ml-auto">
            {timerOpacity}%
          </Badge>
        </div>

        <div className="flex items-center gap-3">
          <span className="text-xs font-medium text-muted-foreground min-w-[24px]">0%</span>
          <Slider
            value={[timerOpacity]}
            min={0}
            max={100}
            step={5}
            className="flex-1"
            onValueChange={(values) => handleOpacityChange(values[0])}
          />
          <Input
            type="number"
            value={timerOpacity}
            onChange={(e) => handleOpacityChange(parseInt(e.target.value) || 10)}
            className="w-14 h-7 text-center text-xs border-border/50 bg-background/50"
            min={0}
            max={100}
          />
          <span className="text-xs font-medium text-muted-foreground min-w-[32px]">100%</span>
        </div>
      </div>

      <Separator className="my-6" />

      {/* Enhanced Preview Section */}
      <div className="mt-2">
        <motion.div
          className="rounded-md border border-border/40 bg-muted/10 overflow-hidden"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, ease: "easeOut" }}
        >
          <div className="flex items-center justify-between px-2.5 py-1.5 border-b border-border/40">
            <h3 className="text-xs font-medium flex items-center gap-1.5 text-foreground/80">
              <motion.div
                animate={{ rotate: isHovered ? 360 : 0 }}
                transition={{ duration: 0.5, ease: "easeInOut" }}
              >
                <Eye className="h-3 w-3 text-primary/70" />
              </motion.div>
              <span>Timer Preview</span>
            </h3>
            <div className="flex items-center gap-1">
              <AnimatePresence mode="wait">
                <motion.div
                  key={timerColor}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.8 }}
                  transition={{ duration: 0.2 }}
                >
                  <Badge variant="outline" className="h-4 px-1.5 text-[10px] font-normal capitalize bg-muted/30 border-border/50">
                    {timerColor}
                  </Badge>
                </motion.div>
              </AnimatePresence>
              <AnimatePresence mode="wait">
                <motion.div
                  key={timerUIStyle}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.8 }}
                  transition={{ duration: 0.2 }}
                >
                  <Badge variant="outline" className="h-4 px-1.5 text-[10px] font-normal bg-muted/30 border-border/50">
                    {timerUIStyle === 'circular' ? 'Circular' : 'Minimal'}
                  </Badge>
                </motion.div>
              </AnimatePresence>
              <motion.div
                initial={{ opacity: 1, scale: 1 }}
                animate={{ opacity: 1, scale: 1 }}
              >
                <Badge variant="outline" className="h-4 px-1.5 text-[10px] font-normal bg-muted/30 border-border/50">
                  {timerOpacity}% opacity
                </Badge>
              </motion.div>
            </div>
          </div>

          <div className={cn("flex flex-col items-center justify-center py-4 px-2.5", gridPatternClass)}>
            {/* Enhanced Timer preview */}
            <motion.div
              className="relative cursor-pointer"
              onHoverStart={() => setIsHovered(true)}
              onHoverEnd={() => setIsHovered(false)}
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.2, ease: "easeOut" }}
            >
              <AnimatePresence mode="wait">
                <motion.div
                  key={`${timerUIStyle}-${timerColor}-${timerOpacity}`}
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.9 }}
                  transition={{ duration: 0.3, ease: "easeOut" }}
                >
                  {timerUIStyle === 'circular' ? (
                    <motion.div
                      className="relative rounded-full overflow-hidden flex items-center justify-center shadow-lg"
                      style={{
                        backgroundColor: `rgba(0, 0, 0, ${timerOpacity / 100})`,
                        width: '5rem',
                        height: '5rem',
                        border: '1px solid rgba(255, 255, 255, 0.15)'
                      }}
                      whileHover={{
                        boxShadow: `0 0 20px ${colorMapping[timerColor]}40`,
                        borderColor: `${colorMapping[timerColor]}60`
                      }}
                      transition={{ duration: 0.3 }}
                    >
                      {/* Animated progress ring */}
                      <motion.div
                        className="absolute inset-0 rounded-full"
                        style={{
                          background: `conic-gradient(${colorMapping[timerColor]} ${animatedProgress}%, transparent ${animatedProgress}%)`,
                          mask: 'radial-gradient(transparent 65%, white 65%)',
                          WebkitMask: 'radial-gradient(transparent 65%, white 65%)'
                        }}
                      />

                      {/* Pulsing center dot */}
                      <motion.div
                        className="absolute inset-0 rounded-full flex items-center justify-center"
                        animate={{
                          scale: isHovered ? [1, 1.1, 1] : 1
                        }}
                        transition={{
                          duration: 1,
                          repeat: isHovered ? Infinity : 0,
                          ease: "easeInOut"
                        }}
                      >
                        <motion.div
                          className="font-mono text-sm font-semibold"
                          style={{ color: colorMapping[timerColor] }}
                          animate={{
                            opacity: isHovered ? [1, 0.7, 1] : 1
                          }}
                          transition={{
                            duration: 1,
                            repeat: isHovered ? Infinity : 0,
                            ease: "easeInOut"
                          }}
                        >
                          {animatedTime}
                        </motion.div>
                      </motion.div>
                    </motion.div>
                  ) : (
                    <motion.div
                      className="relative rounded-md overflow-hidden shadow-lg"
                      style={{
                        backgroundColor: `rgba(0, 0, 0, ${timerOpacity / 100})`,
                        padding: '0.6rem 1.2rem',
                      }}
                      whileHover={{
                        boxShadow: `0 0 20px ${colorMapping[timerColor]}40`,
                        borderColor: `${colorMapping[timerColor]}60`
                      }}
                      transition={{ duration: 0.3 }}
                    >
                      <motion.div
                        className="font-mono text-base font-semibold relative"
                        style={{ color: colorMapping[timerColor] }}
                        animate={{
                          opacity: isHovered ? [1, 0.8, 1] : 1
                        }}
                        transition={{
                          duration: 1.5,
                          repeat: isHovered ? Infinity : 0,
                          ease: "easeInOut"
                        }}
                      >
                        {animatedTime}

                        {/* Animated progress bar */}
                        <motion.div
                          className="absolute bottom-0 left-0 h-[3px] rounded-full"
                          style={{
                            backgroundColor: colorMapping[timerColor],
                          }}
                          initial={{ width: "25%" }}
                          animate={{
                            width: `${animatedProgress}%`,
                            boxShadow: isHovered ? `0 0 8px ${colorMapping[timerColor]}80` : "none"
                          }}
                          transition={{
                            width: { duration: 0.1, ease: "easeOut" },
                            boxShadow: { duration: 0.3 }
                          }}
                        />
                      </motion.div>
                    </motion.div>
                  )}
                </motion.div>
              </AnimatePresence>

              {/* Hover instruction */}
              <AnimatePresence>
                {!isHovered && (
                  <motion.div
                    className="absolute -bottom-8 left-1/2 transform -translate-x-1/2"
                    initial={{ opacity: 0, y: -5 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -5 }}
                    transition={{ duration: 0.2 }}
                  >
                    <span className="text-[9px] text-muted-foreground/60 whitespace-nowrap">
                      Hover to preview animation
                    </span>
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.div>
          </div>
        </motion.div>
      </div>
    </div>
  );
}
